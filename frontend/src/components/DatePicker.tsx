import React, { useState, useEffect, useRef } from 'react';
import { FaCalendarAlt, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

interface DatePickerProps {
  value: string;
  onChange: (date: string) => void;
  name: string;
  placeholder?: string;
  className?: string;
  label?: string;
}

const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  name,
  placeholder = 'اختر تاريخ',
  className = '',
  label
}) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState<Date>(
    value ? new Date(value) : new Date()
  );
  const [calendarPosition, setCalendarPosition] = useState({ top: 0, left: 0 });
  const [showYearPicker, setShowYearPicker] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Calculate calendar position
  const calculateCalendarPosition = () => {
    if (!inputRef.current) return;

    const inputRect = inputRef.current.getBoundingClientRect();
    const calendarHeight = 320; // Approximate calendar height
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - inputRect.bottom;
    const spaceAbove = inputRect.top;

    // Show above if there's more space above or not enough space below
    const showAbove = spaceAbove > spaceBelow && spaceBelow < calendarHeight;

    setCalendarPosition({
      top: showAbove ? inputRect.top - calendarHeight - 8 : inputRect.bottom + 8,
      left: inputRect.right - 240 // Calendar width is 240px (w-60)
    });
  };

  // Close calendar when clicking outside and handle window resize
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setShowCalendar(false);
        setShowYearPicker(false);
      }
    };

    const handleResize = () => {
      if (showCalendar) {
        calculateCalendarPosition();
      }
    };

    const handleScroll = () => {
      if (showCalendar) {
        calculateCalendarPosition();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [showCalendar]);

  // Arabic month names
  const arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  // Arabic day names
  const arabicDays = ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'];

  // Get days in month
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week for first day of month
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  // Format date as YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Parse date from YYYY-MM-DD
  const parseDate = (dateString: string) => {
    if (!dateString) return null;
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day);
  };

  // Handle date selection
  const handleDateSelect = (day: number) => {
    const newDate = new Date(currentMonth);
    newDate.setDate(day);
    onChange(formatDate(newDate));
    setShowCalendar(false);
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  // Year selection functions
  const selectYear = (year: number) => {
    setCurrentMonth(new Date(year, currentMonth.getMonth(), 1));
    setShowYearPicker(false);
  };

  // Generate year range (from 1950 to current year + 10)
  const generateYearRange = () => {
    const currentYear = new Date().getFullYear();
    const startYear = 1950;
    const endYear = currentYear + 10;
    const years = [];

    for (let year = endYear; year >= startYear; year--) {
      years.push(year);
    }

    return years;
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-7 w-7"></div>);
    }

    // Add days of the month
    const selectedDate = parseDate(value);

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const isSelected = selectedDate &&
                         date.getDate() === selectedDate.getDate() &&
                         date.getMonth() === selectedDate.getMonth() &&
                         date.getFullYear() === selectedDate.getFullYear();

      const isToday = new Date().toDateString() === date.toDateString();

      days.push(
        <button
          key={day}
          type="button"
          onClick={() => handleDateSelect(day)}
          className={`h-7 w-7 rounded-md flex items-center justify-center text-xs font-medium transition-all duration-200 hover:scale-105
            ${isSelected
              ? 'bg-primary-600 text-white shadow-md'
              : isToday
                ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-300 dark:border-primary-600'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
        >
          {day}
        </button>
      );
    }

    return days;
  };

  // Format display date in DD/MM/YYYY format with western numerals
  const getDisplayDate = () => {
    if (!value) return '';
    const date = parseDate(value);
    if (!date) return '';

    // Format as DD/MM/YYYY with western numerals
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  return (
    <div className="relative" ref={calendarRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          readOnly
          value={getDisplayDate()}
          placeholder={placeholder}
          onClick={() => {
            calculateCalendarPosition();
            setShowCalendar(!showCalendar);
          }}
          className={`w-full h-10 rounded-xl border-2 px-4 pr-12 transition-all duration-200 ease-in-out bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border-gray-300/60 dark:border-gray-600/40 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 cursor-pointer placeholder:text-gray-400 dark:placeholder:text-gray-500 ${className}`}
        />
        <div
          className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
          onClick={() => setShowCalendar(!showCalendar)}
        >
          <FaCalendarAlt className="text-gray-400 dark:text-gray-500 transition-colors duration-200" />
        </div>

        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={value} />
      </div>

      {showCalendar && (
        <div
          className="fixed z-50 w-60 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-3 backdrop-blur-sm"
          style={{
            top: `${calendarPosition.top}px`,
            left: `${calendarPosition.left}px`
          }}
        >
          <div className="flex justify-between items-center mb-3">
            <button
              type="button"
              onClick={prevMonth}
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 transition-all duration-200"
            >
              <FaChevronRight className="h-3.5 w-3.5" />
            </button>

            <button
              type="button"
              onClick={() => setShowYearPicker(!showYearPicker)}
              className="text-sm font-semibold text-gray-800 dark:text-gray-200 px-3 py-1.5 bg-gray-50 dark:bg-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200 cursor-pointer"
            >
              {arabicMonths[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </button>

            <button
              type="button"
              onClick={nextMonth}
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 transition-all duration-200"
            >
              <FaChevronLeft className="h-3.5 w-3.5" />
            </button>
          </div>

          {!showYearPicker ? (
            <>
              <div className="grid grid-cols-7 gap-1 mb-2">
                {arabicDays.map((day, index) => (
                  <div key={index} className="h-7 w-7 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-md">
                    {day.charAt(0)}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-1 mb-3">
                {generateCalendarDays()}
              </div>
            </>
          ) : (
            <div className="mb-3">
              <div className="max-h-48 overflow-y-auto">
                <div className="grid grid-cols-4 gap-2 p-2">
                  {generateYearRange().map((year) => (
                    <button
                      key={year}
                      type="button"
                      onClick={() => selectYear(year)}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:scale-105 ${
                        year === currentMonth.getFullYear()
                          ? 'bg-primary-600 text-white shadow-md'
                          : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                      }`}
                    >
                      {year}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-between border-t border-gray-200 dark:border-gray-600 pt-2">
            {showYearPicker ? (
              <button
                type="button"
                onClick={() => setShowYearPicker(false)}
                className="px-3 py-1.5 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              >
                عودة للتقويم
              </button>
            ) : (
              <button
                type="button"
                onClick={() => {
                  onChange('');
                  setShowCalendar(false);
                }}
                className="px-3 py-1.5 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              >
                مسح
              </button>
            )}

            <button
              type="button"
              onClick={() => {
                onChange(formatDate(new Date()));
                setShowCalendar(false);
                setShowYearPicker(false);
              }}
              className="px-3 py-1.5 text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900/30 rounded-md transition-all duration-200 font-medium"
            >
              اليوم
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatePicker;
