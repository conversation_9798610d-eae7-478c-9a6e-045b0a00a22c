import React, { useState, useEffect, useRef, forwardRef } from 'react';
import { FaChevronDown, FaCheck, FaExclamationTriangle, FaCheckCircle, FaSearch } from 'react-icons/fa';

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

interface ModalSelectInputProps {
  label?: string;
  name: string;
  id?: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  success?: string;
  icon?: React.ReactNode;
  className?: string;
  searchable?: boolean;
  clearable?: boolean;
}

const ModalSelectInput = forwardRef<HTMLDivElement, ModalSelectInputProps>(({
  label,
  name,
  id,
  value,
  onChange,
  options,
  placeholder = 'اختر خياراً...',
  required = false,
  disabled = false,
  error,
  success,
  icon,
  className = '',
  searchable = false,
  clearable = false
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasInteracted, setHasInteracted] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [dropdownPosition, setDropdownPosition] = useState<'below' | 'above'>('below');
  const [isFocused, setIsFocused] = useState(false);

  // Generate unique ID if not provided
  const selectId = id || `${name}-${Math.random().toString(36).substring(2, 11)}`;

  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // حساب موضع القائمة المنسدلة
  const calculateDropdownPosition = () => {
    if (!selectRef.current) return;

    const rect = selectRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 300; // الارتفاع المتوقع للقائمة

    // البحث عن النافذة المنبثقة الأب
    const modalElement = selectRef.current.closest('[class*="modal"]') ||
                        selectRef.current.closest('[class*="Modal"]') ||
                        selectRef.current.closest('.fixed');

    let containerBottom = viewportHeight;
    if (modalElement) {
      const modalRect = modalElement.getBoundingClientRect();
      containerBottom = modalRect.bottom;
    }

    // المساحة المتاحة أسفل وأعلى المكون
    const spaceBelow = containerBottom - rect.bottom - 20;
    const spaceAbove = rect.top - 20;

    // إذا كانت المساحة أسفل المكون غير كافية والمساحة أعلاه كافية
    if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
      setDropdownPosition('above');
    } else {
      setDropdownPosition('below');
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is outside both the select component and dropdown
      if (selectRef.current && !selectRef.current.contains(target) &&
          dropdownRef.current && !dropdownRef.current.contains(target)) {
        setIsOpen(false);
        setSearchTerm('');
        setIsFocused(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isOpen]);

  // Focus search input when dropdown opens and calculate position
  useEffect(() => {
    if (isOpen) {
      calculateDropdownPosition();
      if (searchable && searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }
  }, [isOpen, searchable]);

  // إعادة حساب الموضع عند تغيير حجم النافذة أو التمرير
  useEffect(() => {
    const handleResize = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    if (isOpen) {
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, true);
      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [isOpen]);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get selected option
  const selectedOption = options.find(option => option.value === value);

  const handleToggle = () => {
    if (!disabled) {
      const newIsOpen = !isOpen;
      if (newIsOpen) {
        // حساب موضع القائمة قبل فتحها
        calculateDropdownPosition();
      }
      setIsOpen(newIsOpen);
      setHasInteracted(true);
      setHighlightedIndex(-1);
      setIsFocused(true);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    // Delay blur to allow for dropdown interactions
    setTimeout(() => {
      if (!isOpen) {
        setIsFocused(false);
      }
    }, 150);
  };

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
    setHasInteracted(true);
    setIsFocused(false);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
    setHasInteracted(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (e.key) {
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        break;
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleSelect(filteredOptions[highlightedIndex].value);
        }
        break;
    }
  };

  // Validation states
  const showError = error && hasInteracted;
  const showSuccess = success && hasInteracted && !error;
  const showRequiredMessage = required && !value && hasInteracted && !error;

  return (
    <div className={`relative ${className}`} ref={ref}>
      {/* Label */}
      {label && (
        <label
          htmlFor={selectId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}

      {/* Select Container */}
      <div className="relative" ref={selectRef}>
        {/* Icon */}
        {icon && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
            <span className="text-gray-400 dark:text-gray-500">{icon}</span>
          </div>
        )}

        {/* Select Button */}
        <button
          type="button"
          id={selectId}
          onClick={handleToggle}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          className={`
            relative w-full h-10 bg-white dark:bg-gray-700 border-2 rounded-xl px-4 pr-10 text-right
            transition-all duration-200 ease-in-out flex items-center
            ${icon ? 'pr-12' : 'pr-10'}
            ${disabled
              ? 'opacity-60 cursor-not-allowed bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              : 'cursor-pointer'
            }
            ${showError
              ? 'border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20'
              : showSuccess
                ? 'border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20'
                : (isOpen || isFocused)
                  ? 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20'
                  : 'border-gray-300/60 dark:border-gray-600/40 hover:border-gray-300/80 dark:hover:border-gray-600/60'
            }
            focus:outline-none
          `}
        >
          <span className={`block truncate ${!selectedOption ? 'text-gray-400 dark:text-gray-500' : ''}`}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
        </button>

        {/* Dropdown Arrow */}
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FaChevronDown className={`h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} />
        </div>

        {/* Clear Button */}
        {clearable && value && !disabled && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute inset-y-0 left-8 flex items-center pl-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <span className="text-lg">×</span>
          </button>
        )}

        {/* Status Icon */}
        {(showError || showSuccess) && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3">
            {showError ? (
              <FaExclamationTriangle className="h-5 w-5 text-red-500" />
            ) : (
              <FaCheckCircle className="h-5 w-5 text-green-500" />
            )}
          </div>
        )}

        {/* Hidden input for form submission */}
        <input type="hidden" name={name} value={value || ''} />

        {/* Dropdown - مدمج مع المكون مع إمكانية الظهور أعلى أو أسفل */}
        {isOpen && (
          <div
            ref={dropdownRef}
            className={`absolute z-[99999] w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden ${
              dropdownPosition === 'above' ? 'bottom-full mb-1' : 'top-full mt-1'
            }`}
            style={{
              maxHeight: dropdownPosition === 'above' ? '250px' : '300px',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              // تأكد من أن القائمة لا تخرج من حدود الشاشة
              maxWidth: '100%',
              minWidth: '100%'
            }}
          >
            {/* Search Input */}
            {searchable && (
              <div className="p-2 border-b border-gray-200 dark:border-gray-700">
                <div className="relative">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-3 w-3" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="بحث..."
                    className="w-full pr-8 pl-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                </div>
              </div>
            )}

            {/* Options */}
            <div
              className="overflow-y-auto dropdown-scrollbar"
              style={{
                maxHeight: searchable
                  ? (dropdownPosition === 'above' ? 'calc(190px - 60px)' : 'calc(240px - 60px)')
                  : (dropdownPosition === 'above' ? '190px' : '240px'),
                minHeight: '40px'
              }}
            >
              {filteredOptions.length === 0 ? (
                <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-center text-sm">
                  لا توجد خيارات متاحة
                </div>
              ) : (
                filteredOptions.map((option, index) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (!option.disabled) {
                        handleSelect(option.value);
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                    }}
                    disabled={option.disabled}
                    className={`
                      w-full px-3 py-2 text-right flex items-center justify-between
                      transition-colors duration-150 ease-in-out
                      ${option.disabled
                        ? 'opacity-50 cursor-not-allowed'
                        : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700'
                      }
                      ${option.value === value
                        ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300'
                        : 'text-gray-900 dark:text-gray-100'
                      }
                      ${highlightedIndex === index
                        ? 'bg-gray-100 dark:bg-gray-700'
                        : ''
                      }
                    `}
                  >
                    <div className="flex items-center">
                      {option.icon && <span className="ml-2">{option.icon}</span>}
                      <span className="truncate">{option.label}</span>
                    </div>
                    {option.value === value && (
                      <FaCheck className="h-3 w-3 text-primary-600 dark:text-primary-400" />
                    )}
                  </button>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {showError && (
        <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
            {error}
          </p>
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-700 dark:text-green-300 text-sm flex items-center">
            <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
            {success}
          </p>
        </div>
      )}

      {/* Required Message */}
      {showRequiredMessage && (
        <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <p className="text-orange-700 dark:text-orange-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-orange-500" />
            هذا الحقل مطلوب، يرجى اختيار قيمة
          </p>
        </div>
      )}
    </div>
  );
});

ModalSelectInput.displayName = 'ModalSelectInput';

export default ModalSelectInput;
